# 🎵 MusicDou 前端开发任务文档

## 📋 项目概述

基于已完成的MusicDou后端系统，构建现代化的音乐分享平台前端应用。后端系统已100%完成，包含完整的用户管理、音乐管理、歌单系统、社交功能、推荐系统等8大核心模块，提供120+个API接口。

## 🎯 前端技术要求

### 核心技术栈
- **框架**: Nuxt.js 3 (Vue 3 + TypeScript)
- **UI框架**: Tailwind CSS + Headless UI
- **状态管理**: Pinia
- **HTTP客户端**: $fetch (Nuxt内置) + ofetch
- **图标库**: Heroicons + Lucide Icons
- **音频播放**: Howler.js
- **动画**: @nuxtjs/motion + CSS Transitions
- **构建工具**: Vite (Nuxt内置)

### 主题系统要求
- **双主题支持**: 黑色主题 + 白色主题
- **自动切换**: 跟随系统主题设置
- **手动切换**: 用户可手动切换主题
- **主题持久化**: 用户选择保存到localStorage
- **平滑过渡**: 主题切换时的动画效果

### 设计风格要求
- **简洁现代**: 简约的界面设计，突出内容
- **响应式设计**: 完美适配桌面、平板、手机
- **无障碍访问**: 符合WCAG 2.1标准
- **性能优化**: 首屏加载时间 < 2秒
- **SEO友好**: 完整的meta标签和结构化数据

## 🏗️ 项目结构设计

```
frontend/
├── components/           # 组件目录
│   ├── ui/              # 基础UI组件
│   ├── layout/          # 布局组件
│   ├── music/           # 音乐相关组件
│   ├── playlist/        # 歌单相关组件
│   ├── user/            # 用户相关组件
│   └── social/          # 社交功能组件
├── pages/               # 页面目录
├── layouts/             # 布局模板
├── middleware/          # 中间件
├── plugins/             # 插件
├── stores/              # Pinia状态管理
├── utils/               # 工具函数
├── types/               # TypeScript类型定义
├── assets/              # 静态资源
├── public/              # 公共文件
├── api/                 # API接口文档 (从后端docs/api移动)
└── composables/         # 组合式函数
```

## 📱 核心功能模块

### 1. 用户认证模块
**页面**: `/login`, `/register`, `/profile`
**功能**:
- 用户登录/注册表单
- JWT token管理
- 用户资料编辑
- 头像上传
- 密码修改
- 每日签到功能

**组件**:
- `LoginForm.vue` - 登录表单
- `RegisterForm.vue` - 注册表单
- `UserProfile.vue` - 用户资料
- `AvatarUpload.vue` - 头像上传
- `DailyCheckin.vue` - 每日签到

### 2. 音乐播放模块
**页面**: `/music`, `/music/[id]`
**功能**:
- 音乐列表展示
- 音乐详情页面
- 音频播放器
- 播放队列管理
- 播放历史记录
- 音质选择

**组件**:
- `MusicPlayer.vue` - 全局音乐播放器
- `MusicList.vue` - 音乐列表
- `MusicCard.vue` - 音乐卡片
- `PlayQueue.vue` - 播放队列
- `VolumeControl.vue` - 音量控制
- `ProgressBar.vue` - 播放进度条

### 3. 歌单管理模块
**页面**: `/playlists`, `/playlists/[id]`, `/playlists/create`
**功能**:
- 歌单列表展示
- 歌单详情页面
- 创建/编辑歌单
- 歌单封面上传
- 歌单收藏功能
- 歌曲添加/移除

**组件**:
- `PlaylistGrid.vue` - 歌单网格
- `PlaylistCard.vue` - 歌单卡片
- `PlaylistForm.vue` - 歌单表单
- `PlaylistDetail.vue` - 歌单详情
- `SongManager.vue` - 歌曲管理

### 4. 社交功能模块
**页面**: `/social`, `/users/[id]`, `/notifications`
**功能**:
- 用户关注/粉丝
- 音乐/歌单评论
- 点赞分享功能
- 用户动态时间线
- 通知中心
- 私信功能

**组件**:
- `UserCard.vue` - 用户卡片
- `FollowButton.vue` - 关注按钮
- `CommentList.vue` - 评论列表
- `CommentForm.vue` - 评论表单
- `ActivityFeed.vue` - 动态时间线
- `NotificationCenter.vue` - 通知中心

### 5. 搜索发现模块
**页面**: `/search`, `/discover`, `/recommendations`
**功能**:
- 全局搜索功能
- 音乐发现页面
- 个性化推荐
- 热门音乐/歌单
- 分类浏览
- 搜索历史

**组件**:
- `SearchBar.vue` - 搜索栏
- `SearchResults.vue` - 搜索结果
- `RecommendationGrid.vue` - 推荐网格
- `CategoryFilter.vue` - 分类筛选
- `TrendingList.vue` - 热门列表

## 🎨 UI/UX 设计规范

### 色彩系统
**浅色主题**:
- Primary: `#3B82F6` (蓝色)
- Secondary: `#8B5CF6` (紫色)
- Background: `#FFFFFF`
- Surface: `#F8FAFC`
- Text: `#1F2937`
- Muted: `#6B7280`

**深色主题**:
- Primary: `#60A5FA` (浅蓝色)
- Secondary: `#A78BFA` (浅紫色)
- Background: `#0F172A`
- Surface: `#1E293B`
- Text: `#F1F5F9`
- Muted: `#94A3B8`

### 字体系统
- **标题**: Inter (700, 600, 500)
- **正文**: Inter (400, 500)
- **代码**: JetBrains Mono (400)

### 间距系统
- **基础单位**: 4px
- **组件间距**: 16px, 24px, 32px
- **页面边距**: 16px (mobile), 24px (tablet), 32px (desktop)

### 圆角系统
- **小圆角**: 4px (按钮、输入框)
- **中圆角**: 8px (卡片、模态框)
- **大圆角**: 16px (图片、特殊容器)

## 🔧 技术实现细节

### 状态管理 (Pinia)
```typescript
// stores/auth.ts - 用户认证状态
// stores/player.ts - 音乐播放状态
// stores/playlist.ts - 歌单状态
// stores/social.ts - 社交功能状态
// stores/theme.ts - 主题状态
```

### API集成
```typescript
// composables/useApi.ts - API请求封装
// composables/useAuth.ts - 认证相关
// composables/usePlayer.ts - 播放器相关
// composables/usePlaylist.ts - 歌单相关
```

### 主题切换实现
```typescript
// composables/useTheme.ts
export const useTheme = () => {
  const colorMode = useColorMode()
  
  const toggleTheme = () => {
    colorMode.preference = colorMode.value === 'dark' ? 'light' : 'dark'
  }
  
  return { colorMode, toggleTheme }
}
```

## 📋 开发任务清单

### 阶段1: 项目初始化 (预计2天)
- [ ] 创建Nuxt.js项目
- [ ] 配置TypeScript和ESLint
- [ ] 安装和配置依赖包
- [ ] 设置项目结构
- [ ] 配置Tailwind CSS
- [ ] 实现主题切换系统

### 阶段2: 基础组件开发 (预计3天)
- [ ] 开发UI基础组件库
- [ ] 实现布局组件
- [ ] 创建导航组件
- [ ] 开发表单组件
- [ ] 实现模态框组件
- [ ] 创建加载状态组件

### 阶段3: 用户认证模块 (预计2天)
- [ ] 实现登录页面
- [ ] 实现注册页面
- [ ] 开发用户资料页面
- [ ] 集成JWT认证
- [ ] 实现路由守卫
- [ ] 添加表单验证

### 阶段4: 音乐播放模块 (预计4天)
- [ ] 开发全局音乐播放器
- [ ] 实现音乐列表页面
- [ ] 创建音乐详情页面
- [ ] 集成Howler.js音频播放
- [ ] 实现播放队列功能
- [ ] 添加播放控制功能

### 阶段5: 歌单管理模块 (预计3天)
- [ ] 实现歌单列表页面
- [ ] 开发歌单详情页面
- [ ] 创建歌单编辑功能
- [ ] 实现歌单收藏功能
- [ ] 添加歌曲管理功能
- [ ] 集成封面上传功能

### 阶段6: 社交功能模块 (预计3天)
- [ ] 实现用户关注功能
- [ ] 开发评论系统
- [ ] 创建点赞分享功能
- [ ] 实现动态时间线
- [ ] 开发通知中心
- [ ] 添加用户互动功能

### 阶段7: 搜索发现模块 (预计2天)
- [ ] 实现全局搜索功能
- [ ] 开发发现页面
- [ ] 创建推荐系统界面
- [ ] 实现分类浏览
- [ ] 添加搜索历史功能
- [ ] 优化搜索体验

### 阶段8: 优化和测试 (预计2天)
- [ ] 性能优化
- [ ] 响应式适配
- [ ] 无障碍访问优化
- [ ] 错误处理完善
- [ ] 单元测试编写
- [ ] 端到端测试

## 🚀 部署配置

### 开发环境
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 类型检查
npm run type-check

# 代码格式化
npm run format
```

### 生产环境
```bash
# 构建生产版本
npm run build

# 预览生产版本
npm run preview

# 静态生成
npm run generate
```

## 📊 性能指标

### 目标指标
- **首屏加载时间**: < 2秒
- **Lighthouse评分**: > 90分
- **包大小**: < 500KB (gzipped)
- **API响应时间**: < 500ms
- **内存使用**: < 100MB

### 优化策略
- 代码分割和懒加载
- 图片优化和懒加载
- API请求缓存
- 组件级缓存
- 服务端渲染(SSR)

## 🔗 API集成说明

前端将集成后端提供的120+个API接口，主要包括:
- 用户认证API (auth.json)
- 音乐管理API (music.json)
- 歌单系统API (playlists.json)
- 社交功能API (social.json)
- 推荐系统API (recommendations.json)
- 通知系统API (notifications.json)
- 文件上传API (upload.json)
- 统计分析API (stats.json)

API文档将从`docs/api`目录移动到前端项目的`api`目录中，便于开发时参考。

## 📝 开发注意事项

1. **类型安全**: 所有API接口都需要TypeScript类型定义
2. **错误处理**: 完善的错误边界和用户友好的错误提示
3. **加载状态**: 所有异步操作都需要加载状态指示
4. **缓存策略**: 合理使用缓存减少不必要的API请求
5. **无障碍访问**: 确保键盘导航和屏幕阅读器支持
6. **SEO优化**: 重要页面需要SSR和meta标签优化
7. **移动端优化**: 触摸友好的交互设计
8. **性能监控**: 集成性能监控和错误追踪

## 🛠️ 详细技术实现

### Nuxt.js配置 (nuxt.config.ts)
```typescript
export default defineNuxtConfig({
  devtools: { enabled: true },
  modules: [
    '@nuxtjs/tailwindcss',
    '@nuxtjs/color-mode',
    '@pinia/nuxt',
    '@nuxtjs/motion',
    '@vueuse/nuxt'
  ],
  colorMode: {
    preference: 'system',
    fallback: 'light',
    hid: 'nuxt-color-mode-script',
    globalName: '__NUXT_COLOR_MODE__',
    componentName: 'ColorScheme',
    classPrefix: '',
    classSuffix: '',
    storageKey: 'nuxt-color-mode'
  },
  css: ['~/assets/css/main.css'],
  runtimeConfig: {
    public: {
      apiBase: process.env.API_BASE_URL || 'http://localhost:3000/api/v1'
    }
  }
})
```

### 主题配置 (tailwind.config.js)
```javascript
module.exports = {
  darkMode: 'class',
  content: [
    './components/**/*.{js,vue,ts}',
    './layouts/**/*.vue',
    './pages/**/*.vue',
    './plugins/**/*.{js,ts}',
    './nuxt.config.{js,ts}',
    './app.vue'
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
          900: '#1e3a8a'
        }
      },
      fontFamily: {
        sans: ['Inter', 'sans-serif'],
        mono: ['JetBrains Mono', 'monospace']
      }
    }
  }
}
```

### 音频播放器实现
```typescript
// composables/useAudioPlayer.ts
export const useAudioPlayer = () => {
  const { Howl } = await import('howler')

  const currentTrack = ref(null)
  const isPlaying = ref(false)
  const volume = ref(0.8)
  const duration = ref(0)
  const currentTime = ref(0)

  const play = (track) => {
    if (currentTrack.value) {
      currentTrack.value.stop()
    }

    currentTrack.value = new Howl({
      src: [track.url],
      html5: true,
      onplay: () => isPlaying.value = true,
      onpause: () => isPlaying.value = false,
      onend: () => isPlaying.value = false,
      onload: () => duration.value = currentTrack.value.duration()
    })

    currentTrack.value.play()
  }

  return {
    currentTrack,
    isPlaying,
    volume,
    duration,
    currentTime,
    play,
    pause: () => currentTrack.value?.pause(),
    stop: () => currentTrack.value?.stop()
  }
}
```

### API请求封装
```typescript
// composables/useApi.ts
export const useApi = () => {
  const config = useRuntimeConfig()
  const { $fetch } = useNuxtApp()

  const api = $fetch.create({
    baseURL: config.public.apiBase,
    onRequest({ request, options }) {
      const token = useCookie('auth-token')
      if (token.value) {
        options.headers = {
          ...options.headers,
          Authorization: `Bearer ${token.value}`
        }
      }
    },
    onResponseError({ response }) {
      if (response.status === 401) {
        navigateTo('/login')
      }
    }
  })

  return { api }
}
```

### 响应式设计断点
```css
/* assets/css/main.css */
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    @apply scroll-smooth;
  }

  body {
    @apply bg-white dark:bg-slate-900 text-gray-900 dark:text-gray-100;
    @apply transition-colors duration-200;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded-lg;
    @apply transition-colors duration-200;
  }

  .card {
    @apply bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-gray-200 dark:border-slate-700;
    @apply transition-colors duration-200;
  }
}

/* 响应式断点 */
@screen sm {
  /* 640px+ */
}

@screen md {
  /* 768px+ */
}

@screen lg {
  /* 1024px+ */
}

@screen xl {
  /* 1280px+ */
}
```

## 🧪 测试策略

### 单元测试 (Vitest)
```typescript
// tests/components/MusicPlayer.test.ts
import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import MusicPlayer from '~/components/music/MusicPlayer.vue'

describe('MusicPlayer', () => {
  it('renders correctly', () => {
    const wrapper = mount(MusicPlayer)
    expect(wrapper.find('.music-player').exists()).toBe(true)
  })

  it('plays music when play button is clicked', async () => {
    const wrapper = mount(MusicPlayer)
    await wrapper.find('.play-button').trigger('click')
    expect(wrapper.emitted('play')).toBeTruthy()
  })
})
```

### E2E测试 (Playwright)
```typescript
// tests/e2e/auth.spec.ts
import { test, expect } from '@playwright/test'

test('user can login', async ({ page }) => {
  await page.goto('/login')
  await page.fill('[data-testid="email"]', '<EMAIL>')
  await page.fill('[data-testid="password"]', 'password123')
  await page.click('[data-testid="login-button"]')
  await expect(page).toHaveURL('/dashboard')
})
```

## 📱 移动端优化

### 触摸手势支持
```typescript
// composables/useSwipe.ts
export const useSwipe = (element: Ref<HTMLElement>) => {
  const { isSwiping, direction } = useSwipeGesture(element, {
    threshold: 50,
    onSwipe(e) {
      if (direction.value === 'left') {
        // 处理左滑
      } else if (direction.value === 'right') {
        // 处理右滑
      }
    }
  })

  return { isSwiping, direction }
}
```

### PWA配置
```typescript
// nuxt.config.ts
export default defineNuxtConfig({
  modules: ['@vite-pwa/nuxt'],
  pwa: {
    registerType: 'autoUpdate',
    workbox: {
      navigateFallback: '/',
      globPatterns: ['**/*.{js,css,html,png,svg,ico}']
    },
    client: {
      installPrompt: true
    },
    manifest: {
      name: 'MusicDou',
      short_name: 'MusicDou',
      description: '现代化音乐分享平台',
      theme_color: '#3b82f6',
      background_color: '#ffffff',
      display: 'standalone',
      icons: [
        {
          src: 'icon-192x192.png',
          sizes: '192x192',
          type: 'image/png'
        }
      ]
    }
  }
})
```

## 🔒 安全考虑

### XSS防护
```typescript
// utils/sanitize.ts
import DOMPurify from 'dompurify'

export const sanitizeHtml = (html: string) => {
  return DOMPurify.sanitize(html)
}
```

### CSRF防护
```typescript
// middleware/csrf.ts
export default defineNuxtRouteMiddleware(() => {
  const csrfToken = useCookie('csrf-token')
  if (!csrfToken.value) {
    throw createError({
      statusCode: 403,
      statusMessage: 'CSRF token missing'
    })
  }
})
```

## 📊 性能监控

### 核心Web指标监控
```typescript
// plugins/performance.client.ts
export default defineNuxtPlugin(() => {
  if (process.client) {
    // 监控LCP (Largest Contentful Paint)
    new PerformanceObserver((list) => {
      const entries = list.getEntries()
      const lastEntry = entries[entries.length - 1]
      console.log('LCP:', lastEntry.startTime)
    }).observe({ entryTypes: ['largest-contentful-paint'] })

    // 监控FID (First Input Delay)
    new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach((entry) => {
        console.log('FID:', entry.processingStart - entry.startTime)
      })
    }).observe({ entryTypes: ['first-input'] })
  }
})
```

---

**文档版本**: v1.0
**创建时间**: 2025-07-31
**预计开发周期**: 3-4周
**开发人员**: 前端开发团队

## 📊 开发进度状态 (更新时间: 2025-07-31 17:45)

### ✅ 已完成阶段

#### Phase 1: Project Setup & Infrastructure (100% 完成)
- ✅ **创建Nuxt.js 3项目** - 使用TypeScript支持，项目运行在 http://localhost:3000
- ✅ **配置开发依赖** - ESLint, Prettier, Tailwind CSS, Pinia, @vueuse/nuxt, Howler.js等
- ✅ **设置项目结构** - 完整的目录结构和基础文件
- ✅ **实现主题系统** - 深色/浅色主题切换，持久化存储
- ✅ **迁移API文档** - 从docs/api移动到musicdou-frontend/api，创建TypeScript接口
- ✅ **设置API集成层** - 完整的API请求、认证和错误处理系统

#### Phase 2: Core Components & UI System (100% 完成)
- ✅ **完整UI组件库** - Button, Input, Card, Modal, Loading, Toast组件
- ✅ **布局系统** - Header导航栏, 默认布局, 响应式设计
- ✅ **页面开发** - 首页, 组件演示页面, 登录/注册页面
- ✅ **主题集成** - 所有组件支持深色/浅色主题切换
- ✅ **技术问题解决** - CSS路径问题, useTheme依赖问题, Icon组件集成

#### Phase 3: Authentication Module (100% 完成)
- ✅ **JWT认证集成** - 完整的token管理、自动刷新、请求拦截器
- ✅ **路由守卫系统** - auth和guest中间件，保护页面访问
- ✅ **用户资料页面** - 个人信息编辑、头像上传、密码修改、账户设置
- ✅ **密码重置功能** - 忘记密码、邮箱验证、安全重置流程
- ✅ **认证状态持久化** - Cookie存储、自动初始化、会话保持
- ✅ **中间件和插件** - 认证保护、访客限制、自动初始化

### 🏗️ 项目当前状态

#### 项目结构
```
musicdou-frontend/
├── api/                     # API文档 (已迁移)
├── assets/css/             # 样式文件
├── components/             # 组件目录
│   ├── ui/                # 基础UI组件
│   ├── layout/            # 布局组件
│   ├── music/             # 音乐相关组件
│   ├── playlist/          # 歌单相关组件
│   ├── user/              # 用户相关组件
│   └── social/            # 社交功能组件
├── composables/           # 组合式函数
├── layouts/               # 布局模板
├── middleware/            # 中间件
├── pages/                 # 页面目录
├── plugins/               # 插件
├── stores/                # Pinia状态管理
├── types/                 # TypeScript类型定义
└── utils/                 # 工具函数
```

#### 核心文件状态
- ✅ `nuxt.config.ts` - 完整配置 (模块、主题、API等)
- ✅ `tailwind.config.js` - 主题色彩、动画配置
- ✅ `package.json` - 所有依赖已安装
- ✅ `types/index.ts` - 完整的TypeScript接口定义
- ✅ `assets/css/main.css` - 完整的样式系统
- ✅ `app.vue` - 主应用布局和主题系统
- ✅ `pages/index.vue` - 首页实现

#### Composables (组合式函数)
- ✅ `useApi.ts` - API请求封装，包含错误处理
- ✅ `useTheme.ts` - 主题管理
- ✅ `useAuthApi.ts` - 认证API服务
- ✅ `useMusicApi.ts` - 音乐API服务
- ✅ `usePlaylistApi.ts` - 歌单API服务
- ✅ `useErrorHandler.ts` - 错误处理系统
- ✅ `useNotification.ts` - 通知系统

#### Stores (状态管理)
- ✅ `stores/auth.ts` - 用户认证状态管理

#### Components (组件)
- ✅ `components/ui/Icon.vue` - 图标组件 (Heroicons集成)

#### 技术栈配置状态
- ✅ **Nuxt.js 4.0.2** - 运行正常
- ✅ **TypeScript** - 严格模式，类型检查已配置
- ✅ **Tailwind CSS** - 深色主题支持，自定义色彩系统
- ✅ **Pinia** - 状态管理已配置
- ✅ **@vueuse/nuxt** - 工具函数库
- ✅ **Howler.js** - 音频播放库
- ✅ **@heroicons/vue** - 图标库
- ✅ **@headlessui/vue** - 无头UI组件

### 🎯 下一阶段任务 (Phase 4: Music Player Module - 0% 完成)

#### 📋 计划功能
- 全局音乐播放器组件开发
- Howler.js音频播放集成
- 播放队列管理系统
- 音乐列表和详情页面
- 播放历史记录功能
- 音质选择和播放模式

#### 🔧 技术准备
- Howler.js音频库已安装配置
- 音乐相关API接口已定义
- 播放器状态管理架构设计
- 音频文件处理和缓存方案

#### 新增文件清单 (Phase 3)
- ✅ `pages/profile.vue` - 用户资料管理页面
- ✅ `pages/forgot-password.vue` - 忘记密码页面
- ✅ `pages/reset-password.vue` - 密码重置页面
- ✅ `middleware/auth.ts` - 认证保护中间件
- ✅ `middleware/guest.ts` - 访客页面中间件
- ✅ `plugins/auth.client.ts` - 认证初始化插件
- ✅ `composables/useAuth.ts` - 完整认证管理系统
- ✅ 更新的类型定义 - 认证相关接口

### 🚀 启动指南

#### 开发环境启动
```bash
cd musicdou-frontend
npm run dev
# 访问 http://localhost:3000
```

#### 可用脚本
```bash
npm run dev          # 启动开发服务器
npm run build        # 构建生产版本
npm run preview      # 预览生产版本
npm run lint         # 代码检查
npm run lint:fix     # 自动修复代码问题
npm run format       # 代码格式化
npm run type-check   # TypeScript类型检查
```

### 📝 开发注意事项

1. **认证系统**: 已完整实现JWT认证，新功能需要考虑认证状态
2. **路由保护**: 使用auth中间件保护需要登录的页面
3. **主题系统**: 已实现深色/浅色主题切换，所有新组件需支持主题
4. **API调用**: 使用composables中的API服务，已包含错误处理和认证
5. **类型安全**: 所有API接口都有TypeScript类型定义
6. **错误处理**: 使用useErrorHandler和useNotification处理错误
7. **状态管理**: 使用Pinia stores管理应用状态

### 🔗 重要文件路径

- **项目根目录**: `/Users/<USER>/Desktop/musicdou-frontend/musicdou-frontend/`
- **开发服务器**: `http://localhost:3000`
- **API文档**: `musicdou-frontend/api/`
- **类型定义**: `musicdou-frontend/types/index.ts`
- **主配置**: `musicdou-frontend/nuxt.config.ts`

---

## 🎯 原始开发计划

此文档为AI开发者提供了完整的技术路线图和实现细节，确保能够构建出高质量的现代化音乐分享平台前端应用。
